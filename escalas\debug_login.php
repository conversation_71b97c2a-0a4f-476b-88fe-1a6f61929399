<?php
/**
 * Script de debugging para diagnosticar problemas de login
 * Uso: Acceder a este archivo directamente desde el navegador
 */

// Configurar para mostrar errores
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔍 DEBUG LOGIN - Sistema Escalas DPI</h1>";
echo "<hr>";

// Verificar conexión a Oracle
echo "<h2>1. 🔗 Verificación de Conexión Oracle</h2>";
try {
    require_once __DIR__ . '/bd/OracleConnection.php';
    $oracle = \BD\OracleConnection::getInstance();
    $connection = $oracle->getConnection();
    
    if ($oracle->isConnectionAlive()) {
        echo "✅ <strong>Conexión Oracle: ACTIVA</strong><br>";
        
        // Probar consulta simple
        $stmt = $connection->query("SELECT SYSDATE FROM DUAL");
        $result = $stmt->fetch();
        echo "✅ <strong>Fecha Oracle:</strong> " . $result['SYSDATE'] . "<br>";
    } else {
        echo "❌ <strong>Conexión Oracle: INACTIVA</strong><br>";
    }
} catch (Exception $e) {
    echo "❌ <strong>Error de conexión Oracle:</strong> " . $e->getMessage() . "<br>";
}

echo "<hr>";

// Verificar tabla USUARIOS_ESCALA
echo "<h2>2. 👥 Verificación de Tabla USUARIOS_ESCALA</h2>";
try {
    $sql = "SELECT COUNT(*) as TOTAL FROM USUARIOS_ESCALA";
    $stmt = $connection->prepare($sql);
    $stmt->execute();
    $result = $stmt->fetch();
    echo "✅ <strong>Total usuarios en USUARIOS_ESCALA:</strong> " . $result['TOTAL'] . "<br>";
    
    // Mostrar algunos usuarios de ejemplo (sin contraseñas)
    $sql = "SELECT CUIL, NOMBRE_COMPLETO, ESTADO FROM USUARIOS_ESCALA WHERE ROWNUM <= 5";
    $stmt = $connection->prepare($sql);
    $stmt->execute();
    $usuarios = $stmt->fetchAll();
    
    echo "<strong>Usuarios de ejemplo:</strong><br>";
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>CUIL</th><th>NOMBRE_COMPLETO</th><th>ESTADO</th></tr>";
    foreach ($usuarios as $user) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($user['CUIL']) . "</td>";
        echo "<td>" . htmlspecialchars($user['NOMBRE_COMPLETO']) . "</td>";
        echo "<td>" . htmlspecialchars($user['ESTADO']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
} catch (Exception $e) {
    echo "❌ <strong>Error consultando USUARIOS_ESCALA:</strong> " . $e->getMessage() . "<br>";
}

echo "<hr>";

// Formulario de prueba de login
echo "<h2>3. 🧪 Prueba de Login</h2>";
echo "<form method='POST' style='margin: 20px 0;'>";
echo "<label>CUIL: <input type='text' name='test_cuil' value='" . ($_POST['test_cuil'] ?? '') . "' placeholder='11 dígitos'></label><br><br>";
echo "<label>Contraseña: <input type='password' name='test_password' value='" . ($_POST['test_password'] ?? '') . "'></label><br><br>";
echo "<input type='submit' name='test_login' value='🔍 Probar Login'>";
echo "</form>";

if (isset($_POST['test_login'])) {
    $testCuil = $_POST['test_cuil'] ?? '';
    $testPassword = $_POST['test_password'] ?? '';
    
    echo "<h3>📋 Resultado de Prueba</h3>";
    echo "<strong>CUIL probado:</strong> " . htmlspecialchars($testCuil) . "<br>";
    echo "<strong>Contraseña:</strong> [" . strlen($testPassword) . " caracteres]<br><br>";
    
    if (empty($testCuil) || empty($testPassword)) {
        echo "❌ <strong>Error:</strong> CUIL y contraseña son requeridos<br>";
    } elseif (!preg_match('/^\d{11}$/', $testCuil)) {
        echo "❌ <strong>Error:</strong> CUIL debe tener exactamente 11 dígitos<br>";
    } else {
        try {
            require_once __DIR__ . '/usuario/UsuarioModel.php';
            $usuarioModel = new UsuarioModel();
            
            echo "<h4>🔍 Proceso de Verificación:</h4>";
            
            // Verificar si el usuario existe
            $sql = "SELECT CUIL, PASSWORD, NOMBRE_COMPLETO, ESTADO FROM USUARIOS_ESCALA WHERE CUIL = :cuil";
            $stmt = $connection->prepare($sql);
            $stmt->bindParam(':cuil', $testCuil);
            $stmt->execute();
            $userData = $stmt->fetch();
            
            if (!$userData) {
                echo "❌ <strong>Usuario NO encontrado</strong> en la base de datos<br>";
            } else {
                echo "✅ <strong>Usuario encontrado:</strong><br>";
                echo "&nbsp;&nbsp;• CUIL: " . htmlspecialchars($userData['CUIL']) . "<br>";
                echo "&nbsp;&nbsp;• Nombre: " . htmlspecialchars($userData['NOMBRE_COMPLETO']) . "<br>";
                echo "&nbsp;&nbsp;• Estado: " . htmlspecialchars($userData['ESTADO']) . "<br>";
                
                $storedPassword = $userData['PASSWORD'] ?? '';
                echo "&nbsp;&nbsp;• Hash almacenado: " . (empty($storedPassword) ? "❌ VACÍO" : "✅ Presente (" . strlen($storedPassword) . " chars)") . "<br>";
                
                if (!empty($storedPassword)) {
                    $info = password_get_info($storedPassword);
                    echo "&nbsp;&nbsp;• Algoritmo hash: " . ($info['algoName'] ?? 'desconocido') . "<br>";
                    
                    // Verificar contraseña
                    if (password_verify($testPassword, $storedPassword)) {
                        echo "✅ <strong>Contraseña CORRECTA</strong><br>";
                    } else {
                        echo "❌ <strong>Contraseña INCORRECTA</strong><br>";
                        
                        // Verificar si es contraseña en texto plano (migración)
                        if (hash_equals((string)$storedPassword, (string)$testPassword)) {
                            echo "⚠️ <strong>Contraseña en texto plano detectada</strong> - Necesita migración<br>";
                        }
                    }
                }
                
                // Verificar estado
                $estadoRaw = isset($userData['ESTADO']) ? trim((string)$userData['ESTADO']) : '';
                $activo = false;
                if ($estadoRaw === '') {
                    $activo = false;
                } elseif (is_numeric($estadoRaw)) {
                    $activo = ((int)$estadoRaw) > 0;
                } else {
                    $estadoUp = strtoupper($estadoRaw);
                    $activo = in_array($estadoUp, ['1', 'A', 'ACTIVO', 'S', 'SI', 'Y', 'TRUE'], true);
                }
                
                echo "&nbsp;&nbsp;• Estado procesado: " . ($activo ? "✅ ACTIVO" : "❌ INACTIVO") . "<br>";
            }
            
            echo "<h4>🔄 Prueba con UsuarioModel:</h4>";
            $resultado = $usuarioModel->verificarCredenciales($testCuil, $testPassword);
            
            if ($resultado === false) {
                echo "❌ <strong>verificarCredenciales() retornó FALSE</strong><br>";
            } elseif (is_array($resultado) && isset($resultado['status']) && $resultado['status'] === 'inactive') {
                echo "⚠️ <strong>Usuario INACTIVO</strong><br>";
            } elseif (is_array($resultado)) {
                echo "✅ <strong>Login EXITOSO</strong><br>";
                echo "<pre>" . print_r($resultado, true) . "</pre>";
            } else {
                echo "❓ <strong>Resultado inesperado:</strong> " . var_export($resultado, true) . "<br>";
            }
            
        } catch (Exception $e) {
            echo "❌ <strong>Error en prueba:</strong> " . $e->getMessage() . "<br>";
            echo "<strong>Stack trace:</strong><br><pre>" . $e->getTraceAsString() . "</pre>";
        }
    }
}

echo "<hr>";

// Verificar logs recientes
echo "<h2>4. 📝 Logs Recientes</h2>";
$logFile = ini_get('error_log');
if ($logFile && file_exists($logFile)) {
    echo "<strong>Archivo de log:</strong> " . $logFile . "<br>";
    $logs = file_get_contents($logFile);
    $lines = explode("\n", $logs);
    $recentLines = array_slice($lines, -20); // Últimas 20 líneas
    
    echo "<strong>Últimas 20 líneas del log:</strong><br>";
    echo "<pre style='background: #f5f5f5; padding: 10px; max-height: 300px; overflow-y: scroll;'>";
    foreach ($recentLines as $line) {
        if (strpos($line, 'LOGIN') !== false || strpos($line, 'CSRF') !== false) {
            echo "<strong>" . htmlspecialchars($line) . "</strong>\n";
        } else {
            echo htmlspecialchars($line) . "\n";
        }
    }
    echo "</pre>";
} else {
    echo "❌ No se pudo acceder al archivo de log<br>";
}

echo "<hr>";
echo "<p><strong>💡 Instrucciones:</strong></p>";
echo "<ol>";
echo "<li>Verifica que la conexión Oracle esté activa</li>";
echo "<li>Confirma que tu CUIL existe en la tabla USUARIOS_ESCALA</li>";
echo "<li>Usa el formulario de prueba para verificar tus credenciales</li>";
echo "<li>Revisa los logs para más detalles</li>";
echo "</ol>";
?>
