<?php
/**
 * Diagnóstico completo del problema de esquemas Oracle
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔍 DIAGNÓSTICO ESQUEMAS ORACLE - Sistema Escalas DPI</h1>";
echo "<hr>";

try {
    require_once __DIR__ . '/bd/OracleConnection.php';
    $oracle = \BD\OracleConnection::getInstance();
    $connection = $oracle->getConnection();
    
    echo "<h2>1. 🔗 Información de Conexión</h2>";
    
    // Obtener información del usuario actual
    $sql = "SELECT USER as USUARIO_ACTUAL, SYS_CONTEXT('USERENV', 'SESSION_USER') as SESSION_USER FROM DUAL";
    $stmt = $connection->prepare($sql);
    $stmt->execute();
    $userInfo = $stmt->fetch();
    
    echo "<strong>Usuario conectado:</strong> " . htmlspecialchars($userInfo['USUARIO_ACTUAL']) . "<br>";
    echo "<strong>Usuario de sesión:</strong> " . htmlspecialchars($userInfo['SESSION_USER']) . "<br>";
    
    echo "<hr>";
    echo "<h2>2. 🔍 Búsqueda de tablas USUARIOS_ESCALA</h2>";
    
    // Buscar en el esquema actual
    echo "<h3>📋 En el esquema actual (USER_TABLES)</h3>";
    $sql = "SELECT TABLE_NAME FROM USER_TABLES WHERE TABLE_NAME LIKE '%ESCALA%' ORDER BY TABLE_NAME";
    $stmt = $connection->prepare($sql);
    $stmt->execute();
    $tablasUsuario = $stmt->fetchAll();
    
    if (empty($tablasUsuario)) {
        echo "❌ <strong>No se encontraron tablas con 'ESCALA' en el esquema actual</strong><br>";
    } else {
        echo "✅ <strong>Tablas encontradas en esquema actual:</strong><br>";
        echo "<ul>";
        foreach ($tablasUsuario as $tabla) {
            echo "<li><strong>" . htmlspecialchars($tabla['TABLE_NAME']) . "</strong></li>";
        }
        echo "</ul>";
    }
    
    // Buscar en TODOS los esquemas (ALL_TABLES)
    echo "<h3>🌐 En TODOS los esquemas (ALL_TABLES)</h3>";
    $sql = "SELECT OWNER, TABLE_NAME FROM ALL_TABLES WHERE TABLE_NAME LIKE '%ESCALA%' ORDER BY OWNER, TABLE_NAME";
    $stmt = $connection->prepare($sql);
    $stmt->execute();
    $todasTablas = $stmt->fetchAll();
    
    if (empty($todasTablas)) {
        echo "❌ <strong>No se encontraron tablas con 'ESCALA' en ningún esquema</strong><br>";
    } else {
        echo "✅ <strong>Tablas encontradas en todos los esquemas:</strong><br>";
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>Esquema (Owner)</th><th>Tabla</th><th>Acceso</th></tr>";
        
        foreach ($todasTablas as $tabla) {
            $owner = $tabla['OWNER'];
            $tableName = $tabla['TABLE_NAME'];
            
            echo "<tr>";
            echo "<td><strong>" . htmlspecialchars($owner) . "</strong></td>";
            echo "<td>" . htmlspecialchars($tableName) . "</td>";
            
            // Probar acceso a la tabla
            try {
                $testSql = "SELECT COUNT(*) as TOTAL FROM {$owner}.{$tableName}";
                $testStmt = $connection->prepare($testSql);
                $testStmt->execute();
                $result = $testStmt->fetch();
                echo "<td style='color: green;'>✅ Accesible ({$result['TOTAL']} registros)</td>";
            } catch (Exception $e) {
                echo "<td style='color: red;'>❌ Sin acceso: " . htmlspecialchars($e->getMessage()) . "</td>";
            }
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<hr>";
    echo "<h2>3. 🎯 Prueba específica: USUARIOS_ESCALA</h2>";
    
    // Intentar diferentes formas de acceder a USUARIOS_ESCALA
    $intentos = [
        "USUARIOS_ESCALA",
        "EXP5.USUARIOS_ESCALA", 
        "USRPRUEBAS.USUARIOS_ESCALA",
        "ADMIN.USUARIOS_ESCALA",
        "SYSTEM.USUARIOS_ESCALA"
    ];
    
    foreach ($intentos as $intento) {
        echo "<h4>🔍 Probando: {$intento}</h4>";
        try {
            $sql = "SELECT COUNT(*) as TOTAL FROM {$intento}";
            $stmt = $connection->prepare($sql);
            $stmt->execute();
            $result = $stmt->fetch();
            
            echo "✅ <strong>ÉXITO:</strong> {$intento} tiene {$result['TOTAL']} registros<br>";
            
            // Si funciona, mostrar algunos registros
            $sql = "SELECT CUIL, NOMBRE_COMPLETO, ESTADO FROM {$intento} WHERE ROWNUM <= 3";
            $stmt = $connection->prepare($sql);
            $stmt->execute();
            $usuarios = $stmt->fetchAll();
            
            if (!empty($usuarios)) {
                echo "<strong>Usuarios de ejemplo:</strong><br>";
                echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
                echo "<tr><th>CUIL</th><th>Nombre</th><th>Estado</th></tr>";
                foreach ($usuarios as $user) {
                    echo "<tr>";
                    echo "<td>" . htmlspecialchars($user['CUIL']) . "</td>";
                    echo "<td>" . htmlspecialchars($user['NOMBRE_COMPLETO']) . "</td>";
                    echo "<td>" . htmlspecialchars($user['ESTADO']) . "</td>";
                    echo "</tr>";
                }
                echo "</table>";
                
                // ¡ENCONTRAMOS LA TABLA CORRECTA!
                echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
                echo "<h3 style='color: #155724; margin: 0;'>🎉 ¡TABLA ENCONTRADA!</h3>";
                echo "<p style='margin: 5px 0;'><strong>Usar en el código:</strong> <code>{$intento}</code></p>";
                echo "</div>";
            }
            
        } catch (Exception $e) {
            echo "❌ <strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "<br>";
        }
        echo "<br>";
    }
    
    echo "<hr>";
    echo "<h2>4. 🔧 Soluciones posibles</h2>";
    echo "<ol>";
    echo "<li><strong>Usar esquema completo:</strong> Cambiar 'USUARIOS_ESCALA' por 'ESQUEMA.USUARIOS_ESCALA'</li>";
    echo "<li><strong>Crear sinónimos:</strong> CREATE SYNONYM USUARIOS_ESCALA FOR ESQUEMA.USUARIOS_ESCALA</li>";
    echo "<li><strong>Otorgar permisos:</strong> GRANT SELECT ON ESQUEMA.USUARIOS_ESCALA TO USUARIO_ACTUAL</li>";
    echo "<li><strong>Cambiar usuario de conexión:</strong> Conectar con el usuario propietario de las tablas</li>";
    echo "</ol>";
    
} catch (Exception $e) {
    echo "❌ <strong>Error crítico:</strong> " . $e->getMessage() . "<br>";
    echo "<strong>Stack trace:</strong><br><pre>" . $e->getTraceAsString() . "</pre>";
}
?>
