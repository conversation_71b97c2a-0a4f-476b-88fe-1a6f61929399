<?php
/**
 * Probar diferentes usuarios de conexión Oracle
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔗 PRUEBA DE CONEXIONES ORACLE</h1>";
echo "<hr>";

// Configuración de conexiones a probar
$conexiones = [
    [
        'nombre' => 'EXP5 (actual)',
        'host' => '**********',
        'port' => '1521',
        'service' => 'DESA920',
        'user' => 'EXP5',
        'pass' => 'E*x20_22.'
    ],
    [
        'nombre' => 'USRPRUEBAS',
        'host' => '**********',
        'port' => '1521',
        'service' => 'DESA920',
        'user' => 'usrpruebas',
        'pass' => 'usrpru3b4s'
    ]
];

foreach ($conexiones as $config) {
    echo "<h2>🔍 Probando conexión: {$config['nombre']}</h2>";
    
    try {
        // Construir DSN
        $dsn = "oci:dbname=//{$config['host']}:{$config['port']}/{$config['service']}";
        
        echo "<strong>DSN:</strong> {$dsn}<br>";
        echo "<strong>Usuario:</strong> {$config['user']}<br>";
        
        // Intentar conexión
        $pdo = new PDO($dsn, $config['user'], $config['pass']);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
        $pdo->setAttribute(PDO::ATTR_CASE, PDO::CASE_UPPER);
        
        echo "✅ <strong>Conexión exitosa</strong><br>";
        
        // Obtener información del usuario
        $stmt = $pdo->prepare("SELECT USER as USUARIO_ACTUAL FROM DUAL");
        $stmt->execute();
        $userInfo = $stmt->fetch();
        echo "<strong>Usuario conectado:</strong> {$userInfo['USUARIO_ACTUAL']}<br>";
        
        // Buscar tablas con ESCALA
        echo "<h3>📋 Tablas con 'ESCALA' en este esquema:</h3>";
        $stmt = $pdo->prepare("SELECT TABLE_NAME FROM USER_TABLES WHERE TABLE_NAME LIKE '%ESCALA%' ORDER BY TABLE_NAME");
        $stmt->execute();
        $tablas = $stmt->fetchAll();
        
        if (empty($tablas)) {
            echo "❌ No se encontraron tablas con 'ESCALA'<br>";
        } else {
            echo "<ul>";
            foreach ($tablas as $tabla) {
                echo "<li><strong>" . htmlspecialchars($tabla['TABLE_NAME']) . "</strong>";
                
                // Probar acceso a la tabla
                try {
                    $testStmt = $pdo->prepare("SELECT COUNT(*) as TOTAL FROM {$tabla['TABLE_NAME']}");
                    $testStmt->execute();
                    $result = $testStmt->fetch();
                    echo " - ✅ {$result['TOTAL']} registros";
                    
                    // Si es USUARIOS_ESCALA, mostrar algunos datos
                    if ($tabla['TABLE_NAME'] === 'USUARIOS_ESCALA') {
                        $testStmt = $pdo->prepare("SELECT CUIL, NOMBRE_COMPLETO FROM USUARIOS_ESCALA WHERE ROWNUM <= 2");
                        $testStmt->execute();
                        $usuarios = $testStmt->fetchAll();
                        
                        echo "<br>&nbsp;&nbsp;&nbsp;👥 Usuarios: ";
                        foreach ($usuarios as $user) {
                            echo htmlspecialchars($user['CUIL']) . " (" . htmlspecialchars($user['NOMBRE_COMPLETO']) . "), ";
                        }
                    }
                    
                } catch (Exception $e) {
                    echo " - ❌ Error: " . $e->getMessage();
                }
                echo "</li>";
            }
            echo "</ul>";
        }
        
        // Buscar en todos los esquemas
        echo "<h3>🌐 Tablas USUARIOS_ESCALA en todos los esquemas:</h3>";
        $stmt = $pdo->prepare("SELECT OWNER, TABLE_NAME FROM ALL_TABLES WHERE TABLE_NAME = 'USUARIOS_ESCALA' ORDER BY OWNER");
        $stmt->execute();
        $todasTablas = $stmt->fetchAll();
        
        if (empty($todasTablas)) {
            echo "❌ No se encontró USUARIOS_ESCALA en ningún esquema<br>";
        } else {
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
            echo "<tr><th>Esquema</th><th>Acceso</th><th>Registros</th></tr>";
            
            foreach ($todasTablas as $tabla) {
                $owner = $tabla['OWNER'];
                echo "<tr>";
                echo "<td><strong>{$owner}</strong></td>";
                
                try {
                    $testStmt = $pdo->prepare("SELECT COUNT(*) as TOTAL FROM {$owner}.USUARIOS_ESCALA");
                    $testStmt->execute();
                    $result = $testStmt->fetch();
                    echo "<td style='color: green;'>✅ Accesible</td>";
                    echo "<td>{$result['TOTAL']}</td>";
                    
                    // Si encontramos una tabla accesible, mostrar la solución
                    if ($result['TOTAL'] > 0) {
                        echo "</tr><tr><td colspan='3' style='background: #d4edda; padding: 10px;'>";
                        echo "<strong>🎉 SOLUCIÓN ENCONTRADA:</strong><br>";
                        echo "Cambiar en el código: <code>USUARIOS_ESCALA</code> → <code>{$owner}.USUARIOS_ESCALA</code>";
                        echo "</td>";
                    }
                    
                } catch (Exception $e) {
                    echo "<td style='color: red;'>❌ Sin acceso</td>";
                    echo "<td>-</td>";
                }
                echo "</tr>";
            }
            echo "</table>";
        }
        
    } catch (Exception $e) {
        echo "❌ <strong>Error de conexión:</strong> " . $e->getMessage() . "<br>";
    }
    
    echo "<hr>";
}

echo "<h2>🔧 Próximos pasos</h2>";
echo "<ol>";
echo "<li><strong>Identifica el esquema correcto</strong> donde están las tablas</li>";
echo "<li><strong>Modifica el código</strong> para usar el esquema completo (ej: ESQUEMA.USUARIOS_ESCALA)</li>";
echo "<li><strong>O crea sinónimos</strong> para simplificar el acceso</li>";
echo "<li><strong>O cambia el usuario de conexión</strong> al propietario de las tablas</li>";
echo "</ol>";
?>
