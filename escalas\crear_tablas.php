<?php
/**
 * Script para crear las tablas necesarias para el sistema de escalas
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🛠️ CREACIÓN DE TABLAS - Sistema Escalas DPI</h1>";
echo "<hr>";

try {
    require_once __DIR__ . '/bd/OracleConnection.php';
    $oracle = \BD\OracleConnection::getInstance();
    $connection = $oracle->getConnection();
    
    echo "<h2>1. 🔍 Verificando tablas existentes...</h2>";
    
    // Verificar si las tablas ya existen
    $tablasNecesarias = ['USUARIOS_ESCALA', 'PERMISOS_ESCALA', 'FUNCIONES_ESCALA', 'LOG_USUARIOS_ESCALA', 'REGISTRO_LOGIN'];
    $tablasExistentes = [];
    
    foreach ($tablasNecesarias as $tabla) {
        $sql = "SELECT COUNT(*) as EXISTE FROM USER_TABLES WHERE TABLE_NAME = :tabla";
        $stmt = $connection->prepare($sql);
        $stmt->bindParam(':tabla', $tabla);
        $stmt->execute();
        $resultado = $stmt->fetch();
        
        if ($resultado['EXISTE'] > 0) {
            $tablasExistentes[] = $tabla;
            echo "✅ <strong>{$tabla}</strong> ya existe<br>";
        } else {
            echo "❌ <strong>{$tabla}</strong> no existe<br>";
        }
    }
    
    if (count($tablasExistentes) === count($tablasNecesarias)) {
        echo "<br>🎉 <strong>Todas las tablas ya existen!</strong><br>";
        echo "<a href='debug_login.php'>🔗 Ir al debug de login</a><br>";
        exit;
    }
    
    echo "<hr>";
    echo "<h2>2. 🛠️ Creando tablas faltantes...</h2>";
    
    // Leer el archivo SQL
    $sqlFile = __DIR__ . '/crear_tabla_usuarios.sql';
    if (!file_exists($sqlFile)) {
        throw new Exception("No se encontró el archivo SQL: {$sqlFile}");
    }
    
    $sqlContent = file_get_contents($sqlFile);
    
    // Dividir en statements individuales
    $statements = explode(';', $sqlContent);
    
    $ejecutados = 0;
    $errores = 0;
    
    foreach ($statements as $statement) {
        $statement = trim($statement);
        
        // Saltar comentarios y líneas vacías
        if (empty($statement) || 
            strpos($statement, '--') === 0 || 
            strpos($statement, 'COMMENT ON') === 0 ||
            strpos($statement, 'SELECT ') === 0) {
            continue;
        }
        
        try {
            echo "<div style='margin: 10px 0; padding: 10px; background: #f0f0f0; border-left: 4px solid #007cba;'>";
            echo "<strong>Ejecutando:</strong> " . substr($statement, 0, 100) . "...<br>";
            
            $stmt = $connection->prepare($statement);
            $stmt->execute();
            
            echo "✅ <span style='color: green;'>Ejecutado correctamente</span>";
            echo "</div>";
            
            $ejecutados++;
            
        } catch (Exception $e) {
            $errores++;
            echo "❌ <span style='color: red;'>Error: " . $e->getMessage() . "</span>";
            echo "</div>";
            
            // Si es error de tabla ya existe, no es crítico
            if (strpos($e->getMessage(), 'ORA-00955') !== false) {
                echo "<small style='color: orange;'>⚠️ La tabla ya existe, continuando...</small><br>";
            }
        }
    }
    
    echo "<hr>";
    echo "<h2>3. 📊 Resumen de ejecución</h2>";
    echo "<ul>";
    echo "<li><strong>Statements ejecutados:</strong> {$ejecutados}</li>";
    echo "<li><strong>Errores:</strong> {$errores}</li>";
    echo "</ul>";
    
    // Verificar que las tablas se crearon correctamente
    echo "<h2>4. ✅ Verificación final</h2>";
    
    foreach ($tablasNecesarias as $tabla) {
        $sql = "SELECT COUNT(*) as EXISTE FROM USER_TABLES WHERE TABLE_NAME = :tabla";
        $stmt = $connection->prepare($sql);
        $stmt->bindParam(':tabla', $tabla);
        $stmt->execute();
        $resultado = $stmt->fetch();
        
        if ($resultado['EXISTE'] > 0) {
            echo "✅ <strong>{$tabla}</strong> creada correctamente<br>";
            
            // Mostrar cantidad de registros
            try {
                $sql = "SELECT COUNT(*) as TOTAL FROM {$tabla}";
                $stmt = $connection->prepare($sql);
                $stmt->execute();
                $count = $stmt->fetch();
                echo "&nbsp;&nbsp;&nbsp;📊 Registros: {$count['TOTAL']}<br>";
            } catch (Exception $e) {
                // Ignorar errores de conteo
            }
        } else {
            echo "❌ <strong>{$tabla}</strong> no se pudo crear<br>";
        }
    }
    
    echo "<hr>";
    echo "<h2>5. 🔐 Usuarios de prueba creados</h2>";
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>CUIL</th><th>Nombre</th><th>Estado</th></tr>";
    
    try {
        $sql = "SELECT CUIL, NOMBRE_COMPLETO, ESTADO FROM USUARIOS_ESCALA";
        $stmt = $connection->prepare($sql);
        $stmt->execute();
        $usuarios = $stmt->fetchAll();
        
        foreach ($usuarios as $usuario) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($usuario['CUIL']) . "</td>";
            echo "<td>" . htmlspecialchars($usuario['NOMBRE_COMPLETO']) . "</td>";
            echo "<td>" . ($usuario['ESTADO'] == '1' ? '✅ Activo' : '❌ Inactivo') . "</td>";
            echo "</tr>";
        }
    } catch (Exception $e) {
        echo "<tr><td colspan='3'>Error consultando usuarios: " . $e->getMessage() . "</td></tr>";
    }
    
    echo "</table>";
    
    echo "<hr>";
    echo "<h2>6. 🚀 Próximos pasos</h2>";
    echo "<ol>";
    echo "<li><strong>Contraseña por defecto:</strong> admin123</li>";
    echo "<li><strong>Cambiar contraseñas:</strong> Usa el sistema para cambiar las contraseñas por defecto</li>";
    echo "<li><strong>Probar login:</strong> <a href='debug_login.php'>🔗 Ir al debug de login</a></li>";
    echo "<li><strong>Configurar usuarios:</strong> Agrega más usuarios según necesites</li>";
    echo "</ol>";
    
} catch (Exception $e) {
    echo "❌ <strong>Error crítico:</strong> " . $e->getMessage() . "<br>";
    echo "<strong>Stack trace:</strong><br><pre>" . $e->getTraceAsString() . "</pre>";
}
?>
