<?php
/**
 * Script para verificar qué tablas de usuarios existen en Oracle
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔍 VERIFICACIÓN DE TABLAS ORACLE</h1>";
echo "<hr>";

try {
    require_once __DIR__ . '/bd/OracleConnection.php';
    $oracle = \BD\OracleConnection::getInstance();
    $connection = $oracle->getConnection();
    
    echo "<h2>1. 📋 Tablas disponibles relacionadas con usuarios</h2>";
    
    // Buscar todas las tablas que contengan 'USUARIO' en el nombre
    $sql = "SELECT TABLE_NAME FROM USER_TABLES WHERE TABLE_NAME LIKE '%USUARIO%' ORDER BY TABLE_NAME";
    $stmt = $connection->prepare($sql);
    $stmt->execute();
    $tablas = $stmt->fetchAll();
    
    if (empty($tablas)) {
        echo "❌ <strong>No se encontraron tablas con 'USUARIO' en el nombre</strong><br><br>";
        
        // Buscar tablas que puedan contener usuarios con otros nombres
        echo "<h3>🔍 Buscando otras tablas posibles...</h3>";
        $sql = "SELECT TABLE_NAME FROM USER_TABLES WHERE TABLE_NAME LIKE '%USER%' OR TABLE_NAME LIKE '%LOGIN%' OR TABLE_NAME LIKE '%AUTH%' ORDER BY TABLE_NAME";
        $stmt = $connection->prepare($sql);
        $stmt->execute();
        $tablas = $stmt->fetchAll();
    }
    
    if (!empty($tablas)) {
        echo "<strong>Tablas encontradas:</strong><br>";
        echo "<ul>";
        foreach ($tablas as $tabla) {
            echo "<li><strong>" . htmlspecialchars($tabla['TABLE_NAME']) . "</strong></li>";
        }
        echo "</ul>";
        
        // Mostrar estructura de cada tabla
        foreach ($tablas as $tabla) {
            $tableName = $tabla['TABLE_NAME'];
            echo "<h3>📊 Estructura de tabla: {$tableName}</h3>";
            
            try {
                $sql = "SELECT COLUMN_NAME, DATA_TYPE, DATA_LENGTH, NULLABLE FROM USER_TAB_COLUMNS WHERE TABLE_NAME = :table_name ORDER BY COLUMN_ID";
                $stmt = $connection->prepare($sql);
                $stmt->bindParam(':table_name', $tableName);
                $stmt->execute();
                $columnas = $stmt->fetchAll();
                
                echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
                echo "<tr><th>Columna</th><th>Tipo</th><th>Longitud</th><th>Nullable</th></tr>";
                foreach ($columnas as $col) {
                    echo "<tr>";
                    echo "<td>" . htmlspecialchars($col['COLUMN_NAME']) . "</td>";
                    echo "<td>" . htmlspecialchars($col['DATA_TYPE']) . "</td>";
                    echo "<td>" . htmlspecialchars($col['DATA_LENGTH']) . "</td>";
                    echo "<td>" . htmlspecialchars($col['NULLABLE']) . "</td>";
                    echo "</tr>";
                }
                echo "</table>";
                
                // Mostrar algunos registros de ejemplo
                $sql = "SELECT * FROM {$tableName} WHERE ROWNUM <= 3";
                $stmt = $connection->prepare($sql);
                $stmt->execute();
                $registros = $stmt->fetchAll();
                
                if (!empty($registros)) {
                    echo "<strong>Registros de ejemplo:</strong><br>";
                    echo "<pre>" . print_r($registros, true) . "</pre>";
                }
                
            } catch (Exception $e) {
                echo "❌ Error consultando tabla {$tableName}: " . $e->getMessage() . "<br>";
            }
        }
    } else {
        echo "❌ <strong>No se encontraron tablas de usuarios</strong><br>";
    }
    
    echo "<hr>";
    echo "<h2>2. 🔍 Todas las tablas disponibles</h2>";
    $sql = "SELECT TABLE_NAME FROM USER_TABLES ORDER BY TABLE_NAME";
    $stmt = $connection->prepare($sql);
    $stmt->execute();
    $todasTablas = $stmt->fetchAll();
    
    echo "<strong>Total de tablas: " . count($todasTablas) . "</strong><br>";
    echo "<details><summary>Ver todas las tablas</summary>";
    echo "<ul>";
    foreach ($todasTablas as $tabla) {
        echo "<li>" . htmlspecialchars($tabla['TABLE_NAME']) . "</li>";
    }
    echo "</ul>";
    echo "</details>";
    
} catch (Exception $e) {
    echo "❌ <strong>Error:</strong> " . $e->getMessage() . "<br>";
    echo "<strong>Stack trace:</strong><br><pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<hr>";
echo "<h2>3. 🛠️ Posibles soluciones</h2>";
echo "<ol>";
echo "<li><strong>Crear la tabla USUARIOS_ESCALA</strong> si no existe</li>";
echo "<li><strong>Usar una tabla existente</strong> y adaptar el código</li>";
echo "<li><strong>Importar datos</strong> desde otra fuente</li>";
echo "</ol>";
?>
